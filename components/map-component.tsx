"use client"

import { useEffect, useRef } from "react"

// Static visitor location data
const visitorLocations = [
  { id: 1, name: "<PERSON><PERSON>", lat: -6.2088, lng: 106.8456, status: "approved" },
  { id: 2, name: "<PERSON><PERSON>", lat: -6.2, lng: 106.83, status: "approved" },
  { id: 3, name: "<PERSON><PERSON>", lat: -6.215, lng: 106.85, status: "pending" },
  { id: 4, name: "<PERSON><PERSON>", lat: -6.195, lng: 106.84, status: "approved" },
  { id: 5, name: "<PERSON><PERSON>", lat: -6.22, lng: 106.86, status: "approved" },
]

export default function MapComponent() {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<any>(null)

  useEffect(() => {
    if (typeof window !== "undefined" && mapRef.current && !mapInstanceRef.current) {
      // Dynamically import Leaflet to avoid SSR issues
      import("leaflet").then((L) => {
        // Initialize map
        const map = L.map(mapRef.current!).setView([-6.2088, 106.8456], 12)

        // Add OpenStreetMap tiles
        L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
          attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        }).addTo(map)

        // Add markers for each visitor location
        visitorLocations.forEach((location) => {
          const markerColor = location.status === "approved" ? "green" : "orange"

          // Create custom icon
          const customIcon = L.divIcon({
            className: "custom-marker",
            html: `<div style="
              background-color: ${markerColor};
              width: 20px;
              height: 20px;
              border-radius: 50%;
              border: 3px solid white;
              box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            "></div>`,
            iconSize: [20, 20],
            iconAnchor: [10, 10],
          })

          const marker = L.marker([location.lat, location.lng], { icon: customIcon }).addTo(map)

          // Add popup with visitor info
          marker.bindPopup(`
            <div style="font-size: 12px;">
              <strong>${location.name}</strong><br/>
              Status: <span style="color: ${markerColor}; font-weight: bold;">
                ${location.status.charAt(0).toUpperCase() + location.status.slice(1)}
              </span>
            </div>
          `)
        })

        mapInstanceRef.current = map

        // Cleanup function
        return () => {
          if (mapInstanceRef.current) {
            mapInstanceRef.current.remove()
            mapInstanceRef.current = null
          }
        }
      })
    }

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
      }
    }
  }, [])

  return (
    <div className="h-64 w-full rounded-lg overflow-hidden border border-gray-200">
      <div ref={mapRef} className="h-full w-full" />
    </div>
  )
}
