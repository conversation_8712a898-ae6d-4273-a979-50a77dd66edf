"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useRouter } from "next/navigation"
import Link from "next/link"

export default function VisitorLoginPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  })
  const [isLoading, setIsLoading] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleLogin = async () => {
    setIsLoading(true)
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Store login state
    localStorage.setItem("visitorLoggedIn", "true")
    localStorage.setItem("visitorEmail", formData.email)

    setIsLoading(false)
    router.push("/visitor/dashboard")
  }

  return (
    <div className="min-h-screen bg-gray-50 px-4 py-8 flex flex-col">
      <div className="max-w-md mx-auto w-full flex-1 flex flex-col justify-center">
        {/* Logo */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Kitabisa</h1>
          <p className="text-gray-600">Visitor Portal</p>
        </div>

        {/* Login Form */}
        <div className="space-y-6">
          {/* Username/Email Input */}
          <div>
            <Input
              type="email"
              placeholder="Email address"
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              className="h-12 bg-white border border-gray-200 rounded-2xl px-4 text-gray-700 placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Password Input */}
          <div>
            <Input
              type="password"
              placeholder="Password"
              value={formData.password}
              onChange={(e) => handleInputChange("password", e.target.value)}
              className="h-12 bg-white border border-gray-200 rounded-2xl px-4 text-gray-700 placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Forgot Password */}
          <div className="text-right">
            <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
              Forgot Password?
            </button>
          </div>

          {/* Login Button */}
          <Button
            onClick={handleLogin}
            disabled={!formData.email || !formData.password || isLoading}
            className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-2xl font-medium text-base shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Signing in...</span>
              </div>
            ) : (
              "Sign In"
            )}
          </Button>

          {/* Divider */}
          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-200"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-gray-50 text-gray-500">Don't have an account?</span>
            </div>
          </div>

          {/* Register Button */}
          <Link href="/visitor/register">
            <Button
              variant="outline"
              className="w-full h-12 border-2 border-blue-600 text-blue-600 hover:bg-blue-50 rounded-2xl font-medium text-base"
            >
              Create Account
            </Button>
          </Link>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center mt-8 text-xs text-gray-500">
        <p>By continuing, you agree to our Terms of Service and Privacy Policy</p>
      </div>
    </div>
  )
}
