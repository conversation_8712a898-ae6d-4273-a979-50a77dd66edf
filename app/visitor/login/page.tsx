"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useRouter } from "next/navigation"
import Link from "next/link"

export default function VisitorLoginPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  })
  const [isLoading, setIsLoading] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleLogin = async () => {
    setIsLoading(true)
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Store login state
    localStorage.setItem("visitorLoggedIn", "true")
    localStorage.setItem("visitorEmail", formData.email)

    setIsLoading(false)
    router.push("/visitor/dashboard")
  }

  return (
    <div className="min-h-screen bg-gray-100 px-6 py-12">
      <div className="max-w-sm mx-auto">
        {/* Logo */}
        <div className="text-center mb-16">
          <h1 className="text-2xl font-bold text-black tracking-wider">COMPANY LOGO</h1>
        </div>

        {/* Login Form */}
        <div className="space-y-6">
          {/* Username/Email Input */}
          <Input
            type="email"
            placeholder="Username/Email"
            value={formData.email}
            onChange={(e) => handleInputChange("email", e.target.value)}
            className="h-14 bg-white border-0 rounded-full px-6 text-gray-600 placeholder:text-gray-400 shadow-sm"
          />

          {/* Password Input */}
          <Input
            type="password"
            placeholder="Password"
            value={formData.password}
            onChange={(e) => handleInputChange("password", e.target.value)}
            className="h-14 bg-white border-0 rounded-full px-6 text-gray-600 placeholder:text-gray-400 shadow-sm"
          />

          {/* Forgot Password */}
          <div className="text-right">
            <button className="text-sm text-gray-500 hover:text-gray-700">Forgot Password?</button>
          </div>

          {/* Login Button */}
          <Button
            onClick={handleLogin}
            disabled={!formData.email || !formData.password || isLoading}
            className="w-full h-14 bg-emerald-500 hover:bg-emerald-600 text-white rounded-full font-medium text-base shadow-lg disabled:opacity-50"
          >
            {isLoading ? "Signing in..." : "Login"}
          </Button>

          {/* Register Button */}
          <Link href="/visitor/register">
            <Button className="w-full h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full font-medium text-base shadow-lg">
              Register
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
