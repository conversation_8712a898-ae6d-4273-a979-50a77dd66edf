"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { FileText, User, Clock, CheckCircle, XCircle, ArrowRight, Home, Plus, Bell, Search } from "lucide-react"
import { useRouter } from "next/navigation"
import Link from "next/link"

// Mock permit data
const mockPermits = [
  {
    id: "PRM-001",
    title: "Main Facility Access",
    status: "active",
    issueDate: "2024-01-15",
    expiryDate: "2024-01-22",
    qrCode: "QR123456",
    location: "Building A, Floor 2",
  },
  {
    id: "PRM-002",
    title: "Mining Area Visit",
    status: "expired",
    issueDate: "2024-01-10",
    expiryDate: "2024-01-17",
    qrCode: "QR789012",
    location: "Mining Site 1",
  },
  {
    id: "PRM-003",
    title: "Safety Training Area",
    status: "pending",
    issueDate: "2024-01-20",
    expiryDate: "2024-01-27",
    qrCode: "QR345678",
    location: "Training Center",
  },
]

export default function VisitorDashboardPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("home")
  const [userEmail, setUserEmail] = useState("")

  useEffect(() => {
    // Check if user is logged in
    const isLoggedIn = localStorage.getItem("visitorLoggedIn")
    if (!isLoggedIn) {
      router.replace("/visitor/login")
      return
    }

    const email = localStorage.getItem("visitorEmail")
    if (email) {
      setUserEmail(email)
    }
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem("visitorLoggedIn")
    localStorage.removeItem("visitorEmail")
    router.replace("/visitor/login")
  }

  const handlePermitClick = (permitId: string) => {
    router.push(`/visitor/permit-detail/${permitId}`)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "expired":
        return "bg-red-100 text-red-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="w-4 h-4" />
      case "expired":
        return <XCircle className="w-4 h-4" />
      case "pending":
        return <Clock className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  // Home Tab - Kitabisa-style design
  const HomeTab = () => (
    <div className="space-y-6">
      {/* Search Bar */}
      <div className="relative">
        <Search className="w-5 h-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
        <input
          type="text"
          placeholder="Cari 'Tolang menolong'"
          className="w-full h-12 pl-12 pr-4 bg-white border border-gray-200 rounded-full text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Hero Banner */}
      <div className="bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl p-6 text-white relative overflow-hidden">
        <div className="relative z-10">
          <h2 className="text-xl font-bold mb-2">Saling Jaga Se-Indonesia 🇮🇩</h2>
          <h3 className="text-lg font-semibold mb-3">Bantu Sesama, Dibantu Bersama</h3>
          <p className="text-sm mb-4 opacity-90">
            Donasi, zakat, dan lindungi keluarga bersama jutaan orang baik melalui aplikasi Kitabisa
          </p>
          <div className="flex space-x-3">
            <div className="bg-black bg-opacity-20 rounded-lg px-3 py-2 flex items-center space-x-2">
              <div className="w-6 h-6 bg-white rounded flex items-center justify-center">
                <span className="text-xs font-bold text-black">📱</span>
              </div>
              <span className="text-xs">Download di App Store</span>
            </div>
            <div className="bg-black bg-opacity-20 rounded-lg px-3 py-2 flex items-center space-x-2">
              <div className="w-6 h-6 bg-white rounded flex items-center justify-center">
                <span className="text-xs font-bold text-black">📱</span>
              </div>
              <span className="text-xs">Download di Google Play</span>
            </div>
          </div>
        </div>
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -translate-y-8 translate-x-8"></div>
        <div className="absolute bottom-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full translate-y-4 translate-x-4"></div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <h3 className="font-semibold text-gray-800 mb-4">Donasi Otomatis, Hanya di Aplikasi Kitabisa</h3>
        <p className="text-sm text-gray-600 mb-4">
          Donasi rutin tanpa lupa dengan fitur Donasi Otomatis di aplikasi Kitabisa. Download sekarang.
        </p>
        <Button className="w-full bg-blue-500 hover:bg-blue-600 text-white rounded-full h-12 font-medium">
          Download aplikasi
        </Button>
      </div>

      {/* Action Categories */}
      <div className="space-y-4">
        <h3 className="font-semibold text-gray-800">Mau berbuat baik apa hari ini?</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-white rounded-xl p-4 text-center shadow-sm border border-gray-100">
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-2xl">💝</span>
            </div>
            <span className="text-sm font-medium text-gray-700">Donasi</span>
          </div>
          <div className="bg-white rounded-xl p-4 text-center shadow-sm border border-gray-100">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-2xl">🕌</span>
            </div>
            <span className="text-sm font-medium text-gray-700">Zakat</span>
          </div>
          <div className="bg-white rounded-xl p-4 text-center shadow-sm border border-gray-100">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-2xl">💰</span>
            </div>
            <span className="text-sm font-medium text-gray-700">Galang Dana</span>
          </div>
          <div className="bg-white rounded-xl p-4 text-center shadow-sm border border-gray-100">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-2xl">🛡️</span>
            </div>
            <span className="text-sm font-medium text-gray-700">Donasi Otomatis</span>
          </div>
        </div>
      </div>
    </div>
  )

  const PermitsTab = () => (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-800">My Permits</h2>
        <Link href="/visitor/register">
          <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-6">New Permit</Button>
        </Link>
      </div>

      {/* Permits List */}
      <div className="space-y-4">
        {mockPermits.map((permit) => (
          <Card
            key={permit.id}
            className="shadow-sm border border-gray-100 cursor-pointer hover:shadow-md transition-shadow rounded-2xl"
            onClick={() => handlePermitClick(permit.id)}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-800 mb-1">{permit.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">{permit.location}</p>
                  <div className="flex items-center space-x-2">
                    <Badge className={`${getStatusColor(permit.status)} border-0 rounded-full`}>
                      <div className="flex items-center space-x-1">
                        {getStatusIcon(permit.status)}
                        <span className="capitalize">{permit.status}</span>
                      </div>
                    </Badge>
                    <span className="text-xs text-gray-500">ID: {permit.id}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="text-right text-sm text-gray-500">
                    <p>Expires</p>
                    <p className="font-medium">{permit.expiryDate}</p>
                  </div>
                  <ArrowRight className="w-5 h-5 text-gray-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )

  const ProfileTab = () => (
    <div className="space-y-6">
      {/* User Info */}
      <Card className="shadow-sm border border-gray-100 rounded-2xl">
        <CardContent className="p-6">
          <div className="flex items-center space-x-4 mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
              <User className="w-8 h-8 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">John Doe</h3>
              <p className="text-gray-600">{userEmail}</p>
              <p className="text-xs text-gray-500">Member since January 2024</p>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="bg-gray-50 rounded-xl p-3">
              <p className="text-lg font-bold text-gray-800">{mockPermits.length}</p>
              <p className="text-xs text-gray-600">Total Permits</p>
            </div>
            <div className="bg-green-50 rounded-xl p-3">
              <p className="text-lg font-bold text-green-600">
                {mockPermits.filter((p) => p.status === "active").length}
              </p>
              <p className="text-xs text-gray-600">Active</p>
            </div>
            <div className="bg-orange-50 rounded-xl p-3">
              <p className="text-lg font-bold text-orange-600">
                {mockPermits.filter((p) => p.status === "pending").length}
              </p>
              <p className="text-xs text-gray-600">Pending</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="space-y-3">
        <button className="w-full bg-white border border-gray-100 rounded-2xl p-4 text-left hover:bg-gray-50 transition-colors shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-blue-600" />
              </div>
              <span className="font-medium text-gray-700">Edit Profile</span>
            </div>
            <ArrowRight className="w-5 h-5 text-gray-400" />
          </div>
        </button>

        <button className="w-full bg-white border border-gray-100 rounded-2xl p-4 text-left hover:bg-gray-50 transition-colors shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <Bell className="w-5 h-5 text-green-600" />
              </div>
              <span className="font-medium text-gray-700">Notification Settings</span>
            </div>
            <ArrowRight className="w-5 h-5 text-gray-400" />
          </div>
        </button>

        <button className="w-full bg-white border border-gray-100 rounded-2xl p-4 text-left hover:bg-gray-50 transition-colors shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                <FileText className="w-5 h-5 text-purple-600" />
              </div>
              <span className="font-medium text-gray-700">Privacy Policy</span>
            </div>
            <ArrowRight className="w-5 h-5 text-gray-400" />
          </div>
        </button>
      </div>

      {/* Logout */}
      <Button
        onClick={handleLogout}
        variant="destructive"
        className="w-full h-12 rounded-2xl font-medium bg-red-500 hover:bg-red-600"
      >
        Sign Out
      </Button>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header - Kitabisa style */}
      <div className="bg-white shadow-sm px-4 py-4 sticky top-0 z-10">
        <div className="max-w-md mx-auto flex items-center justify-between">
          <h1 className="text-xl font-bold text-gray-800">Kitabisa</h1>
          <div className="flex items-center space-x-3">
            <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors">
              <Bell className="w-5 h-5 text-gray-600" />
            </button>
            <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors">
              <User className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-md mx-auto px-4 py-4">
        {activeTab === "home" && <HomeTab />}
        {activeTab === "permits" && <PermitsTab />}
        {activeTab === "profile" && <ProfileTab />}
      </div>

      {/* Bottom Navigation - Kitabisa style */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg">
        <div className="max-w-md mx-auto">
          <div className="flex">
            <button
              onClick={() => setActiveTab("home")}
              className={`flex-1 py-3 px-2 text-center transition-colors ${
                activeTab === "home"
                  ? "text-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <Home className="w-6 h-6 mx-auto mb-1" />
              <span className="text-xs font-medium">Beranda</span>
            </button>
            <button
              onClick={() => setActiveTab("permits")}
              className={`flex-1 py-3 px-2 text-center transition-colors ${
                activeTab === "permits"
                  ? "text-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <FileText className="w-6 h-6 mx-auto mb-1" />
              <span className="text-xs font-medium">Galang Dana</span>
            </button>
            <button
              onClick={() => setActiveTab("permits")}
              className="flex-1 py-3 px-2 text-center transition-colors text-gray-500 hover:text-gray-700"
            >
              <Plus className="w-6 h-6 mx-auto mb-1" />
              <span className="text-xs font-medium">Buat</span>
            </button>
            <button
              onClick={() => setActiveTab("permits")}
              className="flex-1 py-3 px-2 text-center transition-colors text-gray-500 hover:text-gray-700"
            >
              <Search className="w-6 h-6 mx-auto mb-1" />
              <span className="text-xs font-medium">Cari</span>
            </button>
            <button
              onClick={() => setActiveTab("profile")}
              className={`flex-1 py-3 px-2 text-center transition-colors ${
                activeTab === "profile"
                  ? "text-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <User className="w-6 h-6 mx-auto mb-1" />
              <span className="text-xs font-medium">Akun</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
