"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { FileText, User, Clock, CheckCircle, XCircle, ArrowRight } from "lucide-react"
import { useRouter } from "next/navigation"
import Link from "next/link"

// Mock permit data
const mockPermits = [
  {
    id: "PRM-001",
    title: "Main Facility Access",
    status: "active",
    issueDate: "2024-01-15",
    expiryDate: "2024-01-22",
    qrCode: "QR123456",
    location: "Building A, Floor 2",
  },
  {
    id: "PRM-002",
    title: "Mining Area Visit",
    status: "expired",
    issueDate: "2024-01-10",
    expiryDate: "2024-01-17",
    qrCode: "QR789012",
    location: "Mining Site 1",
  },
  {
    id: "PRM-003",
    title: "Safety Training Area",
    status: "pending",
    issueDate: "2024-01-20",
    expiryDate: "2024-01-27",
    qrCode: "QR345678",
    location: "Training Center",
  },
]

export default function VisitorDashboardPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("permits")
  const [userEmail, setUserEmail] = useState("")

  useEffect(() => {
    // Check if user is logged in
    const isLoggedIn = localStorage.getItem("visitorLoggedIn")
    if (!isLoggedIn) {
      router.replace("/visitor/login")
      return
    }

    const email = localStorage.getItem("visitorEmail")
    if (email) {
      setUserEmail(email)
    }
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem("visitorLoggedIn")
    localStorage.removeItem("visitorEmail")
    router.replace("/visitor/login")
  }

  const handlePermitClick = (permitId: string) => {
    router.push(`/visitor/permit-detail/${permitId}`)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "expired":
        return "bg-red-100 text-red-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="w-4 h-4" />
      case "expired":
        return <XCircle className="w-4 h-4" />
      case "pending":
        return <Clock className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  const PermitsTab = () => (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-800">My Permits</h2>
        <Link href="/visitor/register">
          <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-6">New Permit</Button>
        </Link>
      </div>

      {/* Permits List */}
      <div className="space-y-4">
        {mockPermits.map((permit) => (
          <Card
            key={permit.id}
            className="shadow-lg border-0 cursor-pointer hover:shadow-xl transition-shadow"
            onClick={() => handlePermitClick(permit.id)}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-800 mb-1">{permit.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">{permit.location}</p>
                  <div className="flex items-center space-x-2">
                    <Badge className={`${getStatusColor(permit.status)} border-0`}>
                      <div className="flex items-center space-x-1">
                        {getStatusIcon(permit.status)}
                        <span className="capitalize">{permit.status}</span>
                      </div>
                    </Badge>
                    <span className="text-xs text-gray-500">ID: {permit.id}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="text-right text-sm text-gray-500">
                    <p>Expires</p>
                    <p className="font-medium">{permit.expiryDate}</p>
                  </div>
                  <ArrowRight className="w-5 h-5 text-gray-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )

  const ProfileTab = () => (
    <div className="space-y-6">
      {/* Header */}
      <h2 className="text-xl font-semibold text-gray-800">Profile</h2>

      {/* User Info */}
      <Card className="shadow-lg border-0">
        <CardContent className="p-6">
          <div className="flex items-center space-x-4 mb-6">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="w-8 h-8 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">John Doe</h3>
              <p className="text-gray-600">{userEmail}</p>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex justify-between py-3 border-b border-gray-100">
              <span className="text-gray-600">Total Permits</span>
              <span className="font-semibold">{mockPermits.length}</span>
            </div>
            <div className="flex justify-between py-3 border-b border-gray-100">
              <span className="text-gray-600">Active Permits</span>
              <span className="font-semibold text-green-600">
                {mockPermits.filter((p) => p.status === "active").length}
              </span>
            </div>
            <div className="flex justify-between py-3 border-b border-gray-100">
              <span className="text-gray-600">Member Since</span>
              <span className="font-semibold">January 2024</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Settings */}
      <Card className="shadow-lg border-0">
        <CardContent className="p-6 space-y-4">
          <h3 className="font-semibold text-gray-800 mb-4">Settings</h3>

          <button className="w-full text-left py-3 px-4 rounded-lg hover:bg-gray-50 transition-colors">
            <span className="text-gray-700">Edit Profile</span>
          </button>

          <button className="w-full text-left py-3 px-4 rounded-lg hover:bg-gray-50 transition-colors">
            <span className="text-gray-700">Notification Settings</span>
          </button>

          <button className="w-full text-left py-3 px-4 rounded-lg hover:bg-gray-50 transition-colors">
            <span className="text-gray-700">Privacy Policy</span>
          </button>

          <button className="w-full text-left py-3 px-4 rounded-lg hover:bg-gray-50 transition-colors">
            <span className="text-gray-700">Terms of Service</span>
          </button>
        </CardContent>
      </Card>

      {/* Logout */}
      <Button onClick={handleLogout} variant="destructive" className="w-full h-12 rounded-full font-medium">
        Sign Out
      </Button>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-100 pb-24">
      {/* Header */}
      <div className="bg-white shadow-sm px-6 py-6">
        <div className="max-w-sm mx-auto">
          <h1 className="text-2xl font-bold text-gray-800">COMPANY LOGO</h1>
          <p className="text-gray-600 text-sm">Visitor Portal</p>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-sm mx-auto px-6 py-6">{activeTab === "permits" ? <PermitsTab /> : <ProfileTab />}</div>

      {/* Bottom Navigation - Fixed to content width */}
      <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 bg-white border border-gray-200 rounded-t-3xl shadow-lg">
        <div className="w-80 px-4">
          <div className="flex">
            <button
              onClick={() => setActiveTab("permits")}
              className={`flex-1 py-4 px-2 text-center transition-colors ${
                activeTab === "permits"
                  ? "text-blue-600 border-t-2 border-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <FileText className="w-6 h-6 mx-auto mb-1" />
              <span className="text-xs font-medium">Permits</span>
            </button>
            <button
              onClick={() => setActiveTab("profile")}
              className={`flex-1 py-4 px-2 text-center transition-colors ${
                activeTab === "profile"
                  ? "text-blue-600 border-t-2 border-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <User className="w-6 h-6 mx-auto mb-1" />
              <span className="text-xs font-medium">Profile</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
