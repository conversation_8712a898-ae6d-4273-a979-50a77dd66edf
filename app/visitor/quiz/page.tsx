"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { useRouter } from "next/navigation"
import { Play, Pause } from "lucide-react"

const quizQuestions = [
  {
    id: 1,
    question: "What is the primary safety equipment required in the mining area?",
    options: ["Hard Hat", "Safety Glasses", "Reflective", "Steel-Toed Boots"],
  },
  {
    id: 2,
    question: "What should you do in case of an emergency?",
    options: ["Run to the nearest exit", "Call for help immediately", "Wait for instructions", "Continue working"],
  },
  {
    id: 3,
    question: "Which area is restricted for visitors?",
    options: ["Parking lot", "Reception area", "Active mining zone", "Cafeteria"],
  },
  {
    id: 4,
    question: "What is the maximum speed limit in the facility?",
    options: ["10 km/h", "20 km/h", "30 km/h", "40 km/h"],
  },
  {
    id: 5,
    question: "Who should you report to upon arrival?",
    options: ["Security guard", "Site supervisor", "Reception desk", "Any employee"],
  },
]

export default function VisitorQuizPage() {
  const router = useRouter()
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [selectedAnswer, setSelectedAnswer] = useState("")
  const [answers, setAnswers] = useState<string[]>([])
  const [videoTime, setVideoTime] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [showVideo, setShowVideo] = useState(true)
  const [videoWatched, setVideoWatched] = useState(false)

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isPlaying && videoTime < 5) {
      interval = setInterval(() => {
        setVideoTime((prev) => {
          const newTime = prev + 1
          if (newTime >= 5) {
            setIsPlaying(false)
            setVideoWatched(true)
          }
          return newTime
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isPlaying, videoTime])

  const formatTime = (seconds: number) => {
    return `00:0${seconds}`
  }

  const handleVideoToggle = () => {
    if (videoTime >= 5) {
      setVideoTime(0)
    }
    setIsPlaying(!isPlaying)
  }

  const handleAnswerSubmit = () => {
    const newAnswers = [...answers, selectedAnswer]
    setAnswers(newAnswers)

    if (currentQuestion < quizQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
      setSelectedAnswer("")
    } else {
      // All questions answered, proceed to permit
      localStorage.setItem("quizCompleted", "true")
      router.push("/visitor/permit")
    }
  }

  const handleReplayVideo = () => {
    setVideoTime(0)
    setIsPlaying(true)
    setShowVideo(true)
  }

  if (showVideo) {
    return (
      <div className="min-h-screen bg-gray-100 px-6 py-8">
        <div className="max-w-sm mx-auto">
          {/* Logo */}
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-black tracking-wider">COMPANY LOGO</h1>
          </div>

          {/* Video Card */}
          <div className="bg-white rounded-3xl shadow-lg overflow-hidden mb-6">
            {/* Video Player - Optimized */}
            <div className="relative bg-black aspect-video">
              {/* Placeholder Video Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Play className="w-8 h-8 text-white ml-1" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Safety Training Video</h3>
                  <p className="text-sm opacity-80">Watch this important safety briefing</p>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="absolute bottom-0 left-0 right-0 h-1 bg-black bg-opacity-50">
                <div
                  className="h-full bg-blue-500 transition-all duration-1000 ease-linear"
                  style={{ width: `${(videoTime / 5) * 100}%` }}
                ></div>
              </div>

              {/* Play/Pause Overlay */}
              {!isPlaying && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                  <Button
                    onClick={handleVideoToggle}
                    variant="ghost"
                    size="icon"
                    className="w-16 h-16 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all"
                  >
                    <Play className="w-8 h-8 text-white ml-1" />
                  </Button>
                </div>
              )}

              {/* Timer */}
              <div className="absolute top-4 right-4 bg-black bg-opacity-70 text-white px-3 py-1 rounded-full text-sm">
                {formatTime(videoTime)} / 00:05
              </div>

              {/* Pause Button when playing */}
              {isPlaying && (
                <div className="absolute top-4 left-4">
                  <Button
                    onClick={handleVideoToggle}
                    variant="ghost"
                    size="icon"
                    className="w-10 h-10 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 transition-all"
                  >
                    <Pause className="w-5 h-5 text-white" />
                  </Button>
                </div>
              )}
            </div>

            {/* Continue Button */}
            <div className="p-6">
              <Button
                onClick={() => setShowVideo(false)}
                className="w-full h-12 bg-emerald-500 hover:bg-emerald-600 text-white rounded-full font-medium shadow-lg"
                disabled={!videoWatched}
              >
                Continue to Quiz
              </Button>
              {!videoWatched && (
                <p className="text-xs text-gray-500 text-center mt-2">
                  Please watch the complete safety video (5 seconds)
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100 px-6 py-8">
      <div className="max-w-sm mx-auto">
        {/* Logo */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-black tracking-wider">COMPANY LOGO</h1>
        </div>

        {/* Quiz Card */}
        <div className="bg-white rounded-3xl shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-center mb-6">Visitor Safety Quiz</h2>

          <div className="space-y-6">
            <h3 className="font-medium text-gray-800">
              Question {currentQuestion + 1} of {quizQuestions.length}: {quizQuestions[currentQuestion].question}
            </h3>

            <RadioGroup value={selectedAnswer} onValueChange={setSelectedAnswer} className="space-y-4">
              {quizQuestions[currentQuestion].options.map((option, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <RadioGroupItem value={option} id={`option-${index}`} />
                  <Label htmlFor={`option-${index}`} className="cursor-pointer flex-1">
                    {option}
                  </Label>
                </div>
              ))}
            </RadioGroup>

            <Button
              onClick={handleAnswerSubmit}
              disabled={!selectedAnswer}
              className="w-full h-12 bg-emerald-500 hover:bg-emerald-600 text-white rounded-full font-medium shadow-lg"
            >
              Submit Answer
            </Button>

            <p className="text-xs text-gray-500 text-center">Answer all questions to proceed.</p>
          </div>
        </div>

        {/* Replay Video Button */}
        <Button
          onClick={handleReplayVideo}
          className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-white rounded-full font-medium shadow-lg"
        >
          Replay Video Segment
        </Button>
      </div>
    </div>
  )
}
