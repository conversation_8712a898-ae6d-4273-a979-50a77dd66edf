"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Download, Share2, Clock, CheckCircle, XCircle, MapPin, Calendar, User } from "lucide-react"
import { useRouter } from "next/navigation"
import { useParams } from "next/navigation"

// Mock permit data
const mockPermitDetails = {
  "PRM-001": {
    id: "PRM-001",
    title: "Main Facility Access",
    status: "active",
    issueDate: "2024-01-15",
    expiryDate: "2024-01-22",
    qrCode: "QR123456",
    location: "Building A, Floor 2",
    purpose: "Business Meeting",
    host: "<PERSON>",
    department: "Operations",
    duration: "4 hours",
    notes: "Meeting with operations team regarding quarterly review",
  },
  "PRM-002": {
    id: "PRM-002",
    title: "Mining Area Visit",
    status: "expired",
    issueDate: "2024-01-10",
    expiryDate: "2024-01-17",
    qrCode: "QR789012",
    location: "Mining Site 1",
    purpose: "Site Inspection",
    host: "<PERSON>",
    department: "Safety",
    duration: "Full Day",
    notes: "Safety inspection and compliance check",
  },
  "PRM-003": {
    id: "PRM-003",
    title: "Safety Training Area",
    status: "pending",
    issueDate: "2024-01-20",
    expiryDate: "2024-01-27",
    qrCode: "QR345678",
    location: "Training Center",
    purpose: "Training Session",
    host: "Mike Wilson",
    department: "HR",
    duration: "2 hours",
    notes: "Mandatory safety training for new visitors",
  },
}

export default function PermitDetailPage() {
  const router = useRouter()
  const params = useParams()
  const permitId = params.id as string
  const [permit, setPermit] = useState<any>(null)

  useEffect(() => {
    if (permitId && mockPermitDetails[permitId as keyof typeof mockPermitDetails]) {
      setPermit(mockPermitDetails[permitId as keyof typeof mockPermitDetails])
    }
  }, [permitId])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "expired":
        return "bg-red-100 text-red-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="w-5 h-5" />
      case "expired":
        return <XCircle className="w-5 h-5" />
      case "pending":
        return <Clock className="w-5 h-5" />
      default:
        return <Clock className="w-5 h-5" />
    }
  }

  const handleDownload = () => {
    alert("Downloading permit...")
  }

  const handleShare = () => {
    alert("Sharing permit...")
  }

  if (!permit) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading permit details...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100 px-6 py-8">
      <div className="max-w-sm mx-auto">
        {/* Header */}
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" onClick={() => router.back()} className="mr-3">
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-xl font-semibold text-gray-800">Permit Details</h1>
            <p className="text-sm text-gray-600">{permit.id}</p>
          </div>
        </div>

        {/* Status Card */}
        <Card className="shadow-lg border-0 mb-6">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-800">{permit.title}</h2>
              <Badge className={`${getStatusColor(permit.status)} border-0`}>
                <div className="flex items-center space-x-1">
                  {getStatusIcon(permit.status)}
                  <span className="capitalize">{permit.status}</span>
                </div>
              </Badge>
            </div>

            {/* QR Code */}
            <div className="flex justify-center mb-6">
              <div className="w-32 h-32 bg-white border-2 border-gray-300 rounded-2xl flex items-center justify-center shadow-lg">
                <div className="w-24 h-24 bg-black relative rounded-lg">
                  {/* QR Code pattern simulation */}
                  <div className="absolute inset-0 grid grid-cols-8 gap-px p-1">
                    {Array.from({ length: 64 }).map((_, i) => (
                      <div key={i} className={`${Math.random() > 0.5 ? "bg-black" : "bg-white"} rounded-sm`} />
                    ))}
                  </div>
                  {/* Center logo */}
                  <div className="absolute inset-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 bg-emerald-500 rounded-full"></div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3">
              <Button
                onClick={handleDownload}
                className="flex-1 bg-emerald-500 hover:bg-emerald-600 text-white rounded-full"
              >
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
              <Button onClick={handleShare} variant="outline" className="flex-1 rounded-full">
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Details Card */}
        <Card className="shadow-lg border-0 mb-6">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Visit Information</h3>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <MapPin className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Location</p>
                  <p className="font-medium">{permit.location}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Calendar className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Valid Period</p>
                  <p className="font-medium">
                    {permit.issueDate} - {permit.expiryDate}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <User className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Host</p>
                  <p className="font-medium">
                    {permit.host} ({permit.department})
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Clock className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Duration</p>
                  <p className="font-medium">{permit.duration}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Purpose & Notes Card */}
        <Card className="shadow-lg border-0">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Purpose & Notes</h3>

            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600 mb-1">Purpose of Visit</p>
                <p className="font-medium">{permit.purpose}</p>
              </div>

              <div>
                <p className="text-sm text-gray-600 mb-1">Additional Notes</p>
                <p className="text-gray-700">{permit.notes}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
