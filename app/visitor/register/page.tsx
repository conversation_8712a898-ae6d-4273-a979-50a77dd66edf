"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MapPin, Upload, Camera, ArrowLeft, ArrowRight } from "lucide-react"
import { useRouter } from "next/navigation"
import Link from "next/link"

const steps = [
  { id: 1, title: "Personal Info", description: "Basic information" },
  { id: 2, title: "Visit Details", description: "Purpose and duration" },
  { id: 3, title: "Documents", description: "ID and photo verification" },
  { id: 4, title: "Review", description: "Confirm your application" },
]

export default function VisitorRegisterPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    // Personal Info
    fullName: "",
    email: "",
    phone: "",
    company: "",
    position: "",

    // Visit Details
    visitPurpose: "",
    visitDate: "",
    visitTime: "",
    duration: "",
    hostName: "",
    hostDepartment: "",
    additionalNotes: "",

    // Documents
    idCard: null,
    selfie: null,
    location: "",
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = () => {
    // Store registration data
    localStorage.setItem("registrationData", JSON.stringify(formData))
    router.push("/visitor/quiz")
  }

  const StepIndicator = () => (
    <div className="flex items-center justify-between mb-6">
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-center flex-1">
          <div
            className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold shadow-sm ${
              currentStep >= step.id
                ? "bg-blue-600 text-white"
                : currentStep === step.id - 1
                ? "bg-blue-100 text-blue-600 border-2 border-blue-600"
                : "bg-gray-100 text-gray-400"
            }`}
          >
            {step.id}
          </div>
          {index < steps.length - 1 && (
            <div className="flex-1 mx-3">
              <div className={`h-1 rounded-full ${currentStep > step.id ? "bg-blue-600" : "bg-gray-200"}`} />
            </div>
          )}
        </div>
      ))}
    </div>
  )

  const PersonalInfoStep = () => (
    <div className="space-y-5">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
        <Input
          placeholder="Enter your full name"
          value={formData.fullName}
          onChange={(e) => handleInputChange("fullName", e.target.value)}
          className="h-12 bg-gray-50 border border-gray-200 rounded-xl px-4 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
        <Input
          type="email"
          placeholder="Enter your email address"
          value={formData.email}
          onChange={(e) => handleInputChange("email", e.target.value)}
          className="h-12 bg-gray-50 border border-gray-200 rounded-xl px-4 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
        <Input
          placeholder="Enter your phone number"
          value={formData.phone}
          onChange={(e) => handleInputChange("phone", e.target.value)}
          className="h-12 bg-gray-50 border border-gray-200 rounded-xl px-4 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Company/Organization</label>
        <Input
          placeholder="Enter your company name"
          value={formData.company}
          onChange={(e) => handleInputChange("company", e.target.value)}
          className="h-12 bg-gray-50 border border-gray-200 rounded-xl px-4 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Position/Title</label>
        <Input
          placeholder="Enter your position"
          value={formData.position}
          onChange={(e) => handleInputChange("position", e.target.value)}
          className="h-12 bg-gray-50 border border-gray-200 rounded-xl px-4 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
        />
      </div>
    </div>
  )

  const VisitDetailsStep = () => (
    <div className="space-y-5">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Purpose of Visit *</label>
        <Select onValueChange={(value) => handleInputChange("visitPurpose", value)}>
          <SelectTrigger className="h-12 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <SelectValue placeholder="Select purpose of visit" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="business">Business Meeting</SelectItem>
            <SelectItem value="tour">Facility Tour</SelectItem>
            <SelectItem value="training">Training Session</SelectItem>
            <SelectItem value="maintenance">Maintenance Work</SelectItem>
            <SelectItem value="other">Other</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Visit Date *</label>
        <Input
          type="date"
          value={formData.visitDate}
          onChange={(e) => handleInputChange("visitDate", e.target.value)}
          className="h-12 bg-gray-50 border border-gray-200 rounded-xl px-4 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Visit Time *</label>
        <Input
          type="time"
          value={formData.visitTime}
          onChange={(e) => handleInputChange("visitTime", e.target.value)}
          className="h-12 bg-gray-50 border border-gray-200 rounded-xl px-4 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Expected Duration *</label>
        <Select onValueChange={(value) => handleInputChange("duration", value)}>
          <SelectTrigger className="h-12 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <SelectValue placeholder="Select duration" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1hour">1 Hour</SelectItem>
            <SelectItem value="2hours">2 Hours</SelectItem>
            <SelectItem value="halfday">Half Day</SelectItem>
            <SelectItem value="fullday">Full Day</SelectItem>
            <SelectItem value="multiday">Multiple Days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Host Name *</label>
        <Input
          placeholder="Enter host name"
          value={formData.hostName}
          onChange={(e) => handleInputChange("hostName", e.target.value)}
          className="h-12 bg-gray-50 border border-gray-200 rounded-xl px-4 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Host Department</label>
        <Input
          placeholder="Enter host department"
          value={formData.hostDepartment}
          onChange={(e) => handleInputChange("hostDepartment", e.target.value)}
          className="h-12 bg-gray-50 border border-gray-200 rounded-xl px-4 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Additional Notes</label>
        <Textarea
          placeholder="Enter any additional notes"
          value={formData.additionalNotes}
          onChange={(e) => handleInputChange("additionalNotes", e.target.value)}
          className="bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white resize-none"
          rows={3}
        />
      </div>
    </div>
  )

  const DocumentsStep = () => (
    <div className="space-y-6">
      {/* ID Card Upload */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">Upload ID Card *</label>
        <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors bg-gray-50">
          <Upload className="w-10 h-10 text-gray-400 mx-auto mb-3" />
          <p className="text-sm font-medium text-gray-700 mb-1">Click to upload or drag and drop</p>
          <p className="text-xs text-gray-500 mb-4">PNG, JPG up to 5MB</p>
          <input
            type="file"
            accept="image/*"
            onChange={(e) => handleFileUpload("idCard", e.target.files?.[0])}
            className="hidden"
            id="idCard"
          />
          <label htmlFor="idCard" className="cursor-pointer">
            <Button type="button" variant="outline" className="rounded-xl border-2 border-blue-600 text-blue-600 hover:bg-blue-50">
              Choose File
            </Button>
          </label>
          {formData.idCard && (
            <div className="mt-3 flex items-center justify-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <p className="text-sm text-green-600 font-medium">File uploaded successfully</p>
            </div>
          )}
        </div>
      </div>

      {/* Selfie Capture */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">Take a Selfie *</label>
        <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors bg-gray-50">
          <Camera className="w-10 h-10 text-gray-400 mx-auto mb-3" />
          <p className="text-sm font-medium text-gray-700 mb-1">Take a clear photo of yourself</p>
          <p className="text-xs text-gray-500 mb-4">For security verification</p>
          <Button variant="outline" className="rounded-xl border-2 border-blue-600 text-blue-600 hover:bg-blue-50">
            <Camera className="w-4 h-4 mr-2" />
            Capture Photo
          </Button>
        </div>
      </div>

      {/* Location */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">Current Location</label>
        <Button className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-xl shadow-sm">
          <MapPin className="w-4 h-4 mr-2" />
          Get Current Location
        </Button>
      </div>
    </div>
  )

  const ReviewStep = () => (
    <div className="space-y-6">
      {/* Personal Information */}
      <div className="bg-gray-50 rounded-xl p-4 border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Personal Information</h3>
        <div className="space-y-3">
          <div className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
            <span className="text-sm text-gray-600">Full Name</span>
            <span className="font-medium text-gray-800">{formData.fullName || "Not provided"}</span>
          </div>
          <div className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
            <span className="text-sm text-gray-600">Email</span>
            <span className="font-medium text-gray-800">{formData.email || "Not provided"}</span>
          </div>
          <div className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
            <span className="text-sm text-gray-600">Phone</span>
            <span className="font-medium text-gray-800">{formData.phone || "Not provided"}</span>
          </div>
          <div className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
            <span className="text-sm text-gray-600">Company</span>
            <span className="font-medium text-gray-800">{formData.company || "Not provided"}</span>
          </div>
          <div className="flex justify-between items-center py-2">
            <span className="text-sm text-gray-600">Position</span>
            <span className="font-medium text-gray-800">{formData.position || "Not provided"}</span>
          </div>
        </div>
      </div>

      {/* Visit Details */}
      <div className="bg-gray-50 rounded-xl p-4 border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Visit Details</h3>
        <div className="space-y-3">
          <div className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
            <span className="text-sm text-gray-600">Purpose</span>
            <span className="font-medium text-gray-800">{formData.visitPurpose || "Not selected"}</span>
          </div>
          <div className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
            <span className="text-sm text-gray-600">Date</span>
            <span className="font-medium text-gray-800">{formData.visitDate || "Not selected"}</span>
          </div>
          <div className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
            <span className="text-sm text-gray-600">Time</span>
            <span className="font-medium text-gray-800">{formData.visitTime || "Not selected"}</span>
          </div>
          <div className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
            <span className="text-sm text-gray-600">Duration</span>
            <span className="font-medium text-gray-800">{formData.duration || "Not selected"}</span>
          </div>
          <div className="flex justify-between items-center py-2">
            <span className="text-sm text-gray-600">Host</span>
            <span className="font-medium text-gray-800">{formData.hostName || "Not provided"}</span>
          </div>
        </div>
      </div>

      {/* Documents Status */}
      <div className="bg-gray-50 rounded-xl p-4 border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Documents</h3>
        <div className="space-y-3">
          <div className="flex justify-between items-center py-2">
            <span className="text-sm text-gray-600">ID Card</span>
            <div className="flex items-center space-x-2">
              {formData.idCard ? (
                <>
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-green-600">Uploaded</span>
                </>
              ) : (
                <>
                  <XCircle className="w-4 h-4 text-red-600" />
                  <span className="text-sm font-medium text-red-600">Required</span>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <PersonalInfoStep />
      case 2:
        return <VisitDetailsStep />
      case 3:
        return <DocumentsStep />
      case 4:
        return <ReviewStep />
      default:
        return <PersonalInfoStep />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white shadow-sm px-4 py-4">
        <div className="max-w-md mx-auto flex items-center">
          <Link href="/visitor/login">
            <Button variant="ghost" size="icon" className="mr-3 rounded-full">
              <ArrowLeft className="w-5 h-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-xl font-bold text-gray-800">New Permit Application</h1>
            <p className="text-sm text-gray-600">{steps[currentStep - 1].description}</p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 px-4 py-6">
        <div className="max-w-md mx-auto">
          {/* Progress Banner */}
          <div className="bg-gradient-to-br from-blue-500 to-blue-700 rounded-2xl p-6 text-white relative overflow-hidden mb-6">
            <div className="relative z-10">
              <h2 className="text-lg font-bold mb-2">Step {currentStep} of {steps.length}</h2>
              <h3 className="text-xl font-semibold mb-2">{steps[currentStep - 1].title}</h3>
              <p className="text-sm opacity-90">{steps[currentStep - 1].description}</p>
            </div>
            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-24 h-24 bg-white bg-opacity-10 rounded-full -translate-y-4 translate-x-4"></div>
            <div className="absolute bottom-0 right-0 w-16 h-16 bg-white bg-opacity-10 rounded-full translate-y-2 translate-x-2"></div>
          </div>

          {/* Step Indicator */}
          <StepIndicator />

          {/* Form Content */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
            {renderStepContent()}
          </div>

          {/* Navigation Buttons */}
          <div className="flex space-x-3">
            {currentStep > 1 && (
              <Button
                onClick={handlePrevious}
                variant="outline"
                className="flex-1 h-12 rounded-xl border-2 border-gray-300 hover:bg-gray-50"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>
            )}

            {currentStep < steps.length ? (
              <Button
                onClick={handleNext}
                className="flex-1 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-xl shadow-sm"
              >
                Next
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                className="flex-1 h-12 bg-green-600 hover:bg-green-700 text-white rounded-xl shadow-sm"
              >
                Submit Application
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
