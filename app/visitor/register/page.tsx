"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MapPin, Upload, Camera, ArrowLeft, ArrowRight } from "lucide-react"
import { useRouter } from "next/navigation"
import Link from "next/link"

const steps = [
  { id: 1, title: "Personal Info", description: "Basic information" },
  { id: 2, title: "Visit Details", description: "Purpose and duration" },
  { id: 3, title: "Documents", description: "ID and photo verification" },
  { id: 4, title: "Review", description: "Confirm your application" },
]

export default function VisitorRegisterPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    // Personal Info
    fullName: "",
    email: "",
    phone: "",
    company: "",
    position: "",

    // Visit Details
    visitPurpose: "",
    visitDate: "",
    visitTime: "",
    duration: "",
    hostName: "",
    hostDepartment: "",
    additionalNotes: "",

    // Documents
    idCard: null,
    selfie: null,
    location: "",
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = () => {
    // Store registration data
    localStorage.setItem("registrationData", JSON.stringify(formData))
    router.push("/visitor/quiz")
  }

  const StepIndicator = () => (
    <div className="flex items-center justify-between mb-8">
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-center">
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep >= step.id ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"
            }`}
          >
            {step.id}
          </div>
          {index < steps.length - 1 && (
            <div className={`w-8 h-0.5 mx-2 ${currentStep > step.id ? "bg-blue-600" : "bg-gray-200"}`} />
          )}
        </div>
      ))}
    </div>
  )

  const PersonalInfoStep = () => (
    <div className="space-y-4">
      <Input
        placeholder="Full Name *"
        value={formData.fullName}
        onChange={(e) => handleInputChange("fullName", e.target.value)}
        className="h-12 rounded-xl"
      />
      <Input
        type="email"
        placeholder="Email Address *"
        value={formData.email}
        onChange={(e) => handleInputChange("email", e.target.value)}
        className="h-12 rounded-xl"
      />
      <Input
        placeholder="Phone Number *"
        value={formData.phone}
        onChange={(e) => handleInputChange("phone", e.target.value)}
        className="h-12 rounded-xl"
      />
      <Input
        placeholder="Company/Organization"
        value={formData.company}
        onChange={(e) => handleInputChange("company", e.target.value)}
        className="h-12 rounded-xl"
      />
      <Input
        placeholder="Position/Title"
        value={formData.position}
        onChange={(e) => handleInputChange("position", e.target.value)}
        className="h-12 rounded-xl"
      />
    </div>
  )

  const VisitDetailsStep = () => (
    <div className="space-y-4">
      <Select onValueChange={(value) => handleInputChange("visitPurpose", value)}>
        <SelectTrigger className="h-12 rounded-xl">
          <SelectValue placeholder="Purpose of Visit *" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="business">Business Meeting</SelectItem>
          <SelectItem value="tour">Facility Tour</SelectItem>
          <SelectItem value="training">Training Session</SelectItem>
          <SelectItem value="maintenance">Maintenance Work</SelectItem>
          <SelectItem value="other">Other</SelectItem>
        </SelectContent>
      </Select>

      <Input
        type="date"
        placeholder="Visit Date *"
        value={formData.visitDate}
        onChange={(e) => handleInputChange("visitDate", e.target.value)}
        className="h-12 rounded-xl"
      />

      <Input
        type="time"
        placeholder="Visit Time *"
        value={formData.visitTime}
        onChange={(e) => handleInputChange("visitTime", e.target.value)}
        className="h-12 rounded-xl"
      />

      <Select onValueChange={(value) => handleInputChange("duration", value)}>
        <SelectTrigger className="h-12 rounded-xl">
          <SelectValue placeholder="Expected Duration *" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="1hour">1 Hour</SelectItem>
          <SelectItem value="2hours">2 Hours</SelectItem>
          <SelectItem value="halfday">Half Day</SelectItem>
          <SelectItem value="fullday">Full Day</SelectItem>
          <SelectItem value="multiday">Multiple Days</SelectItem>
        </SelectContent>
      </Select>

      <Input
        placeholder="Host Name *"
        value={formData.hostName}
        onChange={(e) => handleInputChange("hostName", e.target.value)}
        className="h-12 rounded-xl"
      />

      <Input
        placeholder="Host Department"
        value={formData.hostDepartment}
        onChange={(e) => handleInputChange("hostDepartment", e.target.value)}
        className="h-12 rounded-xl"
      />

      <Textarea
        placeholder="Additional Notes"
        value={formData.additionalNotes}
        onChange={(e) => handleInputChange("additionalNotes", e.target.value)}
        className="rounded-xl"
        rows={3}
      />
    </div>
  )

  const DocumentsStep = () => (
    <div className="space-y-6">
      {/* ID Card Upload */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Upload ID Card *</label>
        <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center">
          <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600 mb-2">Click to upload or drag and drop</p>
          <Button variant="outline" className="rounded-lg">
            Choose File
          </Button>
        </div>
      </div>

      {/* Selfie Capture */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Take a Selfie *</label>
        <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center">
          <Camera className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600 mb-2">Take a clear photo of yourself</p>
          <Button variant="outline" className="rounded-lg">
            <Camera className="w-4 h-4 mr-2" />
            Capture Photo
          </Button>
        </div>
      </div>

      {/* Location */}
      <Button className="w-full h-12 bg-emerald-500 hover:bg-emerald-600 text-white rounded-xl">
        <MapPin className="w-4 h-4 mr-2" />
        Get Current Location
      </Button>
    </div>
  )

  const ReviewStep = () => (
    <div className="space-y-6">
      <Card className="border-0 bg-gray-50">
        <CardHeader>
          <CardTitle className="text-lg">Personal Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex justify-between">
            <span className="text-gray-600">Name:</span>
            <span className="font-medium">{formData.fullName}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Email:</span>
            <span className="font-medium">{formData.email}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Phone:</span>
            <span className="font-medium">{formData.phone}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Company:</span>
            <span className="font-medium">{formData.company}</span>
          </div>
        </CardContent>
      </Card>

      <Card className="border-0 bg-gray-50">
        <CardHeader>
          <CardTitle className="text-lg">Visit Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex justify-between">
            <span className="text-gray-600">Purpose:</span>
            <span className="font-medium">{formData.visitPurpose}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Date:</span>
            <span className="font-medium">{formData.visitDate}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Time:</span>
            <span className="font-medium">{formData.visitTime}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Duration:</span>
            <span className="font-medium">{formData.duration}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Host:</span>
            <span className="font-medium">{formData.hostName}</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <PersonalInfoStep />
      case 2:
        return <VisitDetailsStep />
      case 3:
        return <DocumentsStep />
      case 4:
        return <ReviewStep />
      default:
        return <PersonalInfoStep />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 px-4 py-6">
      <div className="max-w-md mx-auto">
        {/* Header */}
        <div className="flex items-center mb-6">
          <Link href="/visitor/login">
            <Button variant="ghost" size="icon" className="mr-3">
              <ArrowLeft className="w-5 h-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-xl font-semibold text-gray-800">New Permit Application</h1>
            <p className="text-sm text-gray-600">{steps[currentStep - 1].description}</p>
          </div>
        </div>

        {/* Step Indicator */}
        <StepIndicator />

        {/* Form Content */}
        <Card className="shadow-lg border-0 mb-6">
          <CardHeader>
            <CardTitle>{steps[currentStep - 1].title}</CardTitle>
          </CardHeader>
          <CardContent>{renderStepContent()}</CardContent>
        </Card>

        {/* Navigation Buttons */}
        <div className="flex space-x-3">
          {currentStep > 1 && (
            <Button onClick={handlePrevious} variant="outline" className="flex-1 h-12 rounded-xl">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>
          )}

          {currentStep < steps.length ? (
            <Button onClick={handleNext} className="flex-1 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-xl">
              Next
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              className="flex-1 h-12 bg-green-600 hover:bg-green-700 text-white rounded-xl"
            >
              Submit Application
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
