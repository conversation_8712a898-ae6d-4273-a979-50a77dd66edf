"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Download, Star, MessageCircle, Mail } from "lucide-react"
import { useRouter } from "next/navigation"

export default function VisitorPermitPage() {
  const router = useRouter()
  const [rating, setRating] = useState(0)
  const [feedback, setFeedback] = useState("")
  const [visitorData, setVisitorData] = useState<any>(null)

  useEffect(() => {
    // Get visitor data from localStorage
    const data = localStorage.getItem("visitorData")
    if (data) {
      setVisitorData(JSON.parse(data))
    }
  }, [])

  const handleStarClick = (starIndex: number) => {
    setRating(starIndex + 1)
  }

  const handleFeedbackSubmit = () => {
    // Store feedback
    const feedbackData = {
      rating,
      feedback,
      timestamp: new Date().toISOString(),
    }
    localStorage.setItem("visitorFeedback", JSON.stringify(feedbackData))
    alert("Thank you for your feedback!")
  }

  const handleShare = (platform: "whatsapp" | "email") => {
    const message = `I have received my digital visitor permit for ${visitorData?.fullName || "Visitor"}`

    if (platform === "whatsapp") {
      window.open(`https://wa.me/?text=${encodeURIComponent(message)}`, "_blank")
    } else {
      window.open(`mailto:?subject=Digital Visitor Permit&body=${encodeURIComponent(message)}`, "_blank")
    }
  }

  const handleDownload = (format: "pdf" | "jpg") => {
    // Simulate download
    alert(`Downloading permit as ${format.toUpperCase()}...`)
  }

  return (
    <div className="min-h-screen bg-gray-100 px-6 py-8">
      <div className="max-w-sm mx-auto space-y-6">
        {/* Logo */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-black tracking-wider">COMPANY LOGO</h1>
        </div>

        {/* Digital Permit Card */}
        <div className="bg-white rounded-3xl shadow-lg p-6">
          <h2 className="text-lg font-semibold mb-6">Digital Permit</h2>

          {/* QR Code */}
          <div className="flex justify-center mb-6">
            <div className="w-32 h-32 bg-white border-2 border-gray-300 rounded-2xl flex items-center justify-center shadow-lg">
              <div className="w-24 h-24 bg-black relative rounded-lg">
                {/* QR Code pattern simulation */}
                <div className="absolute inset-0 grid grid-cols-8 gap-px p-1">
                  {Array.from({ length: 64 }).map((_, i) => (
                    <div key={i} className={`${Math.random() > 0.5 ? "bg-black" : "bg-white"} rounded-sm`} />
                  ))}
                </div>
                {/* Center logo */}
                <div className="absolute inset-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 bg-emerald-500 rounded-full"></div>
              </div>
            </div>
          </div>

          {/* Download Buttons */}
          <div className="flex space-x-3">
            <Button
              onClick={() => handleDownload("pdf")}
              className="flex-1 bg-emerald-500 hover:bg-emerald-600 text-white rounded-full shadow-lg"
            >
              <Download className="w-4 h-4 mr-2" />
              Download PDF
            </Button>
            <Button
              onClick={() => handleDownload("jpg")}
              className="flex-1 bg-emerald-500 hover:bg-emerald-600 text-white rounded-full shadow-lg"
            >
              <Download className="w-4 h-4 mr-2" />
              Download JPG
            </Button>
          </div>
        </div>

        {/* Feedback Card */}
        <div className="bg-white rounded-3xl shadow-lg p-6">
          <h2 className="text-lg font-semibold mb-6">Feedback</h2>

          {/* Star Rating */}
          <div className="flex justify-center space-x-2 mb-6">
            {[0, 1, 2, 3, 4].map((starIndex) => (
              <button key={starIndex} onClick={() => handleStarClick(starIndex)} className="focus:outline-none">
                <Star className={`w-8 h-8 ${starIndex < rating ? "text-orange-400 fill-current" : "text-gray-300"}`} />
              </button>
            ))}
          </div>

          {/* Comment Box */}
          <Textarea
            placeholder="Leave your comments here..."
            value={feedback}
            onChange={(e) => setFeedback(e.target.value)}
            rows={4}
            className="resize-none rounded-2xl border-gray-200 mb-6"
          />

          {/* Submit Feedback Button */}
          <Button
            onClick={handleFeedbackSubmit}
            className="w-full bg-orange-500 hover:bg-orange-600 text-white rounded-full shadow-lg"
          >
            Submit Feedback
          </Button>
        </div>

        {/* Share Permit Card */}
        <div className="bg-white rounded-3xl shadow-lg p-6">
          <h2 className="text-lg font-semibold mb-6">Share Permit</h2>

          <div className="flex space-x-3">
            <Button
              onClick={() => handleShare("whatsapp")}
              className="flex-1 bg-emerald-500 hover:bg-emerald-600 text-white rounded-full shadow-lg"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              WhatsApp
            </Button>
            <Button
              onClick={() => handleShare("email")}
              className="flex-1 bg-emerald-500 hover:bg-emerald-600 text-white rounded-full shadow-lg"
            >
              <Mail className="w-4 h-4 mr-2" />
              Email
            </Button>
          </div>
        </div>

        {/* Back to Dashboard */}
        <div className="text-center">
          <Button
            onClick={() => router.push("/visitor/dashboard")}
            variant="outline"
            className="rounded-full border-gray-300"
          >
            Back to Dashboard
          </Button>
        </div>
      </div>
    </div>
  )
}
